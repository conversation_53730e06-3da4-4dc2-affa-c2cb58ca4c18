<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import LLMPanel from '@/screens/chart-builder/llm-panel/LLMPanel.vue';
import AIChatPanelHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanelHeader.vue';
import AIChatInput from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatInput.vue';
import AIConversation from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIConversation.vue';
import { OpenAiMessageRole } from '@core/chat-bot/domain/OpenAiMessageRole';
import { ChatMessageData } from '@/screens/dashboard-detail/intefaces/chatbot/ChatMessageData';
import { MessageType } from '@/screens/dashboard-detail/intefaces/chatbot/MessageType';

@Component({
  components: { AIConversation, AIChatInput, AIChatPanelHeader, LLMPanel }
})
export default class AIChatPanel extends Vue {
  protected mainData: ChatMessageData[] = [
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    },
    {
      role: OpenAiMessageRole.user,
      type: MessageType.text,
      text: 'Hello'
    },

    {
      role: OpenAiMessageRole.assistant,
      type: MessageType.text,
      text: 'World'
    }
  ];
}
</script>

<template>
  <div id="ai-chat-panel">
    <AIChatPanelHeader id="ai-chat-panel-header" />
    <AIConversation id="ai-conversation" :mainData="mainData" :botTyping="false" />
    <AIChatInput id="ai-chat-input" />
  </div>
</template>

<style scoped lang="scss">
#ai-chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  #ai-chat-panel-header {
    flex-shrink: 0; // Header stays at top, doesn't shrink
  }

  #ai-conversation {
    flex: 1; // Takes up remaining space
    overflow: hidden; // Prevents overflow, scrolling handled by AIConversation component
    min-height: 0; // Allows flex item to shrink below content size
  }

  #ai-chat-input {
    flex-shrink: 0; // Input stays at bottom, doesn't shrink
  }
}
</style>
